import { injectable, BindingScope } from '@loopback/core';
import { repository } from '@loopback/repository';
import {
  UserLocationRoleRepository,
  LocationOneRepository,
  LocationTwoRepository,
  LocationThreeRepository,
  LocationFourRepository,
} from '../repositories';

@injectable({ scope: BindingScope.TRANSIENT })
export class LocationFilterService {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
  ) { }

  async getLocationFilterConditions(userId: string): Promise<any[][]> {
    const userRoles = await this.userLocationRoleRepository.find({
      where: { userId },
    });

    const filterConditions = await Promise.all(
      userRoles.map(async userLocation => {
        const conditions: any[] = [];

        if (userLocation.locationOneId) {
          let locationOneIds: string[] = [];

          if (userLocation.locationOneId === 'tier1-all') {
            const allLocationOnes = await this.locationOneRepository.find();
            locationOneIds = allLocationOnes.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationOneIds.push(userLocation.locationOneId);
          }

          if (locationOneIds.length > 0) {
            conditions.push({ locationOneId: { inq: locationOneIds } });
          }
        }

        if (userLocation.locationTwoId) {
          let locationTwoIds: string[] = [];

          if (userLocation.locationTwoId === 'tier2-all') {
            const allLocationTwos = await this.locationTwoRepository.find({
              where: { locationOneId: userLocation.locationOneId },
            });
            locationTwoIds = allLocationTwos.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationTwoIds.push(userLocation.locationTwoId);
          }

          if (locationTwoIds.length > 0) {
            conditions.push({ locationTwoId: { inq: locationTwoIds } });
          }
        }

        if (userLocation.locationThreeId) {
          let locationThreeIds: string[] = [];

          if (userLocation.locationThreeId === 'tier3-all') {
            const allLocationThrees = await this.locationThreeRepository.find({
              where: { locationTwoId: userLocation.locationTwoId },
            });
            locationThreeIds = allLocationThrees.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationThreeIds.push(userLocation.locationThreeId);
          }

          if (locationThreeIds.length > 0) {
            conditions.push({ locationThreeId: { inq: locationThreeIds } });
          }
        }

        if (userLocation.locationFourId) {
          let locationFourIds: string[] = [];

          if (userLocation.locationFourId === 'tier4-all') {
            const allLocationFours = await this.locationFourRepository.find({
              where: { locationThreeId: userLocation.locationThreeId },
            });
            locationFourIds = allLocationFours.map(loc => loc.id).filter((id): id is string => Boolean(id));
          } else {
            locationFourIds.push(userLocation.locationFourId);
          }

          if (locationFourIds.length > 0) {
            conditions.push({ locationFourId: { inq: locationFourIds } });
          }
        }

        return conditions.length > 0 ? conditions : null;
      })
    );

    // Filter out any null or empty condition arrays
    return filterConditions.filter((conditions): conditions is any[] => conditions !== null);
  }

  async getMatchingUserLocationRolesByRoleAndLocations(
    roleId: string,
    locationOneId: string,
    locationTwoId: string,
    locationThreeId: string,
    locationFourId: string,
  ): Promise<any[]> {
    const userRoles = await this.userLocationRoleRepository.find();

    const matchedRoles = await Promise.all(
      userRoles.map(async role => {
        // Must contain the given role ID
        if (!role.roles?.includes(roleId)) return null;

        // TIER 1: locationOneId
        const tier1Match =
          role.locationOneId === 'tier1-all' || role.locationOneId === locationOneId;
        if (!tier1Match) return null;

        // TIER 2: locationTwoId (only valid under the Tier 1 in role)
        let tier2Match = false;
        if (role.locationTwoId === 'tier2-all') {
          const t2 = await this.locationTwoRepository.find({
            where: {
              locationOneId: role.locationOneId === 'tier1-all'
                ? locationOneId
                : role.locationOneId,
            },
          });
          tier2Match = t2.some(loc => loc.id === locationTwoId);
        } else {
          tier2Match = role.locationTwoId === locationTwoId;
        }
        if (!tier2Match) return null;

        // TIER 3: locationThreeId (only valid under Tier 2 in role)
        let tier3Match = false;
        if (role.locationThreeId === 'tier3-all') {
          const t3 = await this.locationThreeRepository.find({
            where: {
              locationTwoId: role.locationTwoId === 'tier2-all'
                ? locationTwoId
                : role.locationTwoId,
            },
          });
          tier3Match = t3.some(loc => loc.id === locationThreeId);
        } else {
          tier3Match = role.locationThreeId === locationThreeId;
        }
        if (!tier3Match) return null;

        // TIER 4: locationFourId (only valid under Tier 3 in role)
        let tier4Match = false;
        if (role.locationFourId === 'tier4-all') {
          const t4 = await this.locationFourRepository.find({
            where: {
              locationThreeId: role.locationThreeId === 'tier3-all'
                ? locationThreeId
                : role.locationThreeId,
            },
          });
          tier4Match = t4.some(loc => loc.id === locationFourId);
        } else {
          tier4Match = role.locationFourId === locationFourId;
        }
        if (!tier4Match) return null;

        return role;
      })
    );

    return matchedRoles.filter(r => r !== null);
  }


}
