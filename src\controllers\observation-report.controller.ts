import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { ObservationReport, Action } from '../models';
import { ObservationReportRepository, ActionRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import moment from "moment";
import { SqsService } from '../services/sqs-service.service';
import { LocationFilterService } from '../services';

// Define the interfaces at the top of your file
interface CategoryStatistics {
  [key: string]: number; // This will hold the counts for each category
}

interface CategoryPercentages {
  [key: string]: string; // This will hold the percentage strings for each category
}

interface TypeConditionStatistics {
  [key: string]: number;
}

interface GhsCounts {
  [key: string]: number;
}

interface DetailedResults {
  closedOnTime: ObservationReport[];
  overdue: ObservationReport[];
  rectifiedOnSpot: ObservationReport[];
  firstInstanceClosure: ObservationReport[];
  [key: string]: ObservationReport[];
}

@authenticate('jwt')
export class ObservationReportController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @post('/observation-reports')
  @response(200, {
    description: 'ObservationReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(ObservationReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReport',
            exclude: ['id'],
          }),
        },
      },
    })
    observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user?.id) {
      observationReport.submittedId = user.id;
    }
    const count = await this.observationReportRepository.count();
    observationReport.maskId = `OBS-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`;
    let status = '';


    if (observationReport.type === 'Safe') {
      status = 'Safe - Closed';
    } else {
      status = 'Reported';
    }

    const obsReport = await this.observationReportRepository.create(observationReport);

    if (obsReport.type === 'At Risk' && obsReport.rectifiedStatus === 'No') {

      const actionItem = {
        application: "Observation",
        actionType: "action_owner",
        comments: obsReport.actionTaken,
        description: obsReport.description,
        dueDate: obsReport.dueDate,
        actionToBeTaken: obsReport.actionToBeTaken,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: obsReport.id,
        submittedById: user?.id,
        assignedToId: obsReport.actionOwnerId
      }

      if (obsReport.actionOwnerId) {
        const actionOwner = await this.userRepository.findById(obsReport.actionOwnerId);
        const updatedObsReport = await this.observationReportRepository.findById(obsReport.id,
          {
            include: [
              { relation: 'locationOne' },
              { relation: 'locationTwo' },
              { relation: 'locationThree' },
              { relation: 'locationFour' },
              { relation: 'locationFive' },
              { relation: 'locationSix' },

            ]
          })
        const locationOneName = (updatedObsReport as any).locationOne?.name;
        const locationTwoName = (updatedObsReport as any).locationTwo?.name;
        const locationThreeName = (updatedObsReport as any).locationThree?.name;
        const locationFourName = (updatedObsReport as any).locationFour?.name;
        const locationFiveName = (updatedObsReport as any).locationFive?.name;
        const locationSixName = (updatedObsReport as any).locationSix?.name;

        const mailSubject = `${updatedObsReport.maskId} Take Action:  ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <h1>Observation Report</h1>
            <p><strong>ID:</strong> ${updatedObsReport.maskId}</p>
            <p><strong>Take Action:</strong> ${locationFiveName}, ${locationSixName}</p>
            <p><strong>Name of the Reporter:</strong> ${user?.firstName}</p>
            <p><strong>Location:</strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
            <p><strong>Description:</strong> ${obsReport.description}</p>
            <p><strong>Action to be Taken:</strong> ${obsReport.actionToBeTaken}</p>
            <p><strong>Assignee's Action Due Date and Time:</strong> ${obsReport.dueDate}</p>
            <p>Please login to the EHS Digital Application or AcuiZen Mobile Application to take action </p>
        </body>
        </html>`;

        if (actionOwner) { await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }



      await this.actionRepository.create(actionItem)
      status = 'At Risk - Actions Assigned';
    } else {
      if (observationReport.type !== 'Safe')
        status = 'At Risk - Closed';
    }
    await this.observationReportRepository.updateById(obsReport.id, { status: status })
    return obsReport;
  }

  @get('/observation-reports/count')
  @response(200, {
    description: 'ObservationReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.count(where);
  }

  @get('/observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      return this.observationReportRepository.find({ ...filter, where: { submittedId: user?.id } });
    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/observation-reports-by-locations')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, { includeRelations: true }),
        },
      },
    },
  })
  async findByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);

      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      const observationReports = await this.observationReportRepository.find({
        ...filter,
        where: whereClause
      });
      return observationReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @authenticate.skip()
  @post('/observations/process')
  @response(200, {
    description: 'Process observations and return statistics',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async processObservations(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};
    const results: DetailedResults = {
      closedOnTime: [],
      overdue: [],
      rectifiedOnSpot: [],
      firstInstanceClosure: [],
    };

    try {
      const observations = await this.observationReportRepository.find({
        include: [
          { relation: 'submitted' },
          { relation: 'actions' },
          { relation: 'workActivity' },
          { relation: 'ghsOne' },
          { relation: 'ghsTwo' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          { relation: 'actionOwner' },
        ],
      });

      // Filter observations by date range if provided
      const filteredObservations = observations.filter((observation) => {
        const createdDate = moment(observation.created);
        if (from && to) {
          return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
        } else if (from) {
          return createdDate.isSameOrAfter(moment(from), 'day');
        } else if (to) {
          return createdDate.isSameOrBefore(moment(to), 'day');
        }
        return true;
      });

      filteredObservations.forEach((observation) => {
        if (!observation) return;

        const isClosed = observation?.status?.toLowerCase()?.includes('closed') || false;
        const isRectified = observation?.rectifiedStatus === 'Yes';

        if (isRectified) {
          results.rectifiedOnSpot.push(observation);
        }

        if (observation.actions && observation.actions.length > 0) {
          const validActions = observation.actions.filter((action) => action.createdDate);

          const sortedActions = validActions.sort((a, b) =>
            new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
          );

          const lastAction = sortedActions.length > 0 ? sortedActions[0] : null;

          if (lastAction?.dueDate) {
            const dueDatePassed = new Date(lastAction.dueDate) < new Date();

            if (dueDatePassed && isClosed) {
              results.closedOnTime.push(observation);
            }

            if (dueDatePassed && !isClosed) {
              results.overdue.push(observation);
            }

            if (
              observation.actions.every(
                (action) => action.status && !action.status.toLowerCase().includes('returned')
              )
            ) {
              results.firstInstanceClosure.push(observation);
            }
          }
        }
      });

      const firstInstanceClosureRate =
        filteredObservations.length > 0
          ? (results.firstInstanceClosure.length / filteredObservations.length) * 100
          : 0;

      return {
        headCount: filteredObservations.length,
        closedOnTimeCount: results.closedOnTime.length,
        overdueCount: results.overdue.length,
        rectifiedOnSpotCount: results.rectifiedOnSpot.length,
        firstInstanceClosureRate,
      };

    } catch (error) {
      console.error('Error processing observations:', error);
      throw new HttpErrors.InternalServerError('Failed to process observations. Please try again later.');
    }
  }

  @authenticate.skip()
  @post('/observations/detailed-results')
  @response(200, {
    description: 'Get detailed results for specific observation types',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getDetailedResults(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
              type: {
                type: 'string',
                enum: ['closedOnTime', 'overdue', 'rectifiedOnSpot', 'firstInstanceClosure'],
                description: 'Type of detailed results to return'
              },
            },
            required: ['type'],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string; type: string }
  ): Promise<object> {
    const { from, to, type } = filterParams;

    try {
      const observations = await this.observationReportRepository.find({
        include: [
          { relation: 'submitted' },
          { relation: 'actions' },
          { relation: 'workActivity' },
          { relation: 'ghsOne' },
          { relation: 'ghsTwo' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          { relation: 'actionOwner' },
        ],
      });

      // Filter observations by date range if provided
      const filteredObservations = observations.filter((observation) => {
        const createdDate = moment(observation.created);
        if (from && to) {
          return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
        } else if (from) {
          return createdDate.isSameOrAfter(moment(from), 'day');
        } else if (to) {
          return createdDate.isSameOrBefore(moment(to), 'day');
        }
        return true;
      });

      const results: DetailedResults = {
        closedOnTime: [],
        overdue: [],
        rectifiedOnSpot: [],
        firstInstanceClosure: [],
      };

      filteredObservations.forEach((observation) => {
        if (!observation) return;

        const isClosed = observation?.status?.toLowerCase()?.includes('closed') || false;
        const isRectified = observation?.rectifiedStatus === 'Yes';

        if (isRectified) {
          results.rectifiedOnSpot.push(observation);
        }

        if (observation.actions && observation.actions.length > 0) {
          const validActions = observation.actions.filter((action) => action.createdDate);

          const sortedActions = validActions.sort((a, b) =>
            new Date(b.createdDate!).getTime() - new Date(a.createdDate!).getTime()
          );

          const lastAction = sortedActions.length > 0 ? sortedActions[0] : null;

          if (lastAction?.dueDate) {
            const dueDatePassed = new Date(lastAction.dueDate) < new Date();

            if (dueDatePassed && isClosed) {
              results.closedOnTime.push(observation);
            }

            if (dueDatePassed && !isClosed) {
              results.overdue.push(observation);
            }

            if (
              observation.actions.every(
                (action) => action.status && !action.status.toLowerCase().includes('returned')
              )
            ) {
              results.firstInstanceClosure.push(observation);
            }
          }
        }
      });

      // Return only the requested type
      if (!results[type]) {
        throw new HttpErrors.BadRequest(`Invalid type: ${type}. Valid types are: closedOnTime, overdue, rectifiedOnSpot, firstInstanceClosure`);
      }

      return {
        type,
        count: results[type].length,
        data: results[type],
      };

    } catch (error) {
      console.error('Error getting detailed results:', error);
      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }
      throw new HttpErrors.InternalServerError('Failed to get detailed results. Please try again later.');
    }
  }
  //Observation general ID 64ee1bb5ac4ab50570dfaf94

  @authenticate.skip()
  @post('/observations/category-statistics')
  @response(200, {
    description: 'Calculate and return the percentage distribution of observation categories',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getCategoryStatistics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};
    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations by date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    const categoryCounts: CategoryStatistics = filteredObservations.reduce((acc: CategoryStatistics, observation) => {
      const category: string = observation.category || 'Unknown'; // Default to 'Unknown' if category is not specified
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    const totalObservations = filteredObservations.length;
    const categoryPercentages: CategoryPercentages = Object.keys(categoryCounts).reduce((acc: CategoryPercentages, key) => {
      acc[key] = ((categoryCounts[key] / totalObservations) * 100).toFixed(2) + '%';
      return acc;
    }, {});

    return {
      totalCategories: Object.keys(categoryCounts).length,
      categoryPercentages,
      totalObservations,
    };
  }

  @authenticate.skip()
  @post('/observations/type-condition-statistics')
  @response(200, {
    description: 'Calculate and return the distribution of observations by type and condition action',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getTypeConditionStatistics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // No required fields; both `from` and `to` are optional
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    // Destructure from and to, and default to an empty object if undefined
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations by date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    const typeConditionCounts: TypeConditionStatistics = filteredObservations.reduce(
      (acc: TypeConditionStatistics, observation) => {
        try {
          const remarks = JSON.parse(observation.remarks || '{}');
          const conditionAct = remarks.condition_act || 'Unknown';
          const combinedKey = `${observation.type} (${conditionAct})`;

          // Ignore keys containing "Unknown"
          if (!combinedKey.includes('Unknown')) {
            acc[combinedKey] = (acc[combinedKey] || 0) + 1;
          }
        } catch (error) {
          console.error('Error parsing remarks:', error);
        }
        return acc;
      },
      {} as TypeConditionStatistics
    );

    const filteredResponse = {
      totalObservations: filteredObservations.length,
      totalCombinations: Object.keys(typeConditionCounts).length,
      typeConditionCounts,
    };

    return filteredResponse;
  }

  @authenticate.skip()
  @post('/observations/type-condition-statistics-monthly')
  @response(200, {
    description: 'Calculate and return monthly distribution of observations by type and condition action',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getTypeConditionStatisticsMonthly(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // No required fields
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations based on the date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    // Initialize typeConditionCounts as a record of year-months and counts
    const monthlyCounts: Record<string, Record<string, number>> = {};

    filteredObservations.forEach((observation) => {
      try {
        const remarks = JSON.parse(observation.remarks || '{}');
        const conditionAct = remarks.condition_act || 'Unknown';
        const combinedKey = `${observation.type} (${conditionAct})`;

        // Safely parse the date
        const createdDate = observation.created ? new Date(observation.created) : new Date(0);
        const yearMonth = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}`;

        // Skip 'Unknown' combinations
        if (!combinedKey.includes('Unknown')) {
          if (!monthlyCounts[yearMonth]) {
            monthlyCounts[yearMonth] = {};
          }
          monthlyCounts[yearMonth][combinedKey] = (monthlyCounts[yearMonth][combinedKey] || 0) + 1;
        }
      } catch (error) {
        console.error('Error parsing remarks or date:', error);
      }
    });

    // Build the final response
    const formattedResponse = {
      totalMonths: Object.keys(monthlyCounts).length,
      monthlyTypeConditionCounts: monthlyCounts,
    };

    return formattedResponse;
  }

  @authenticate.skip()
  @post('/observations/top-ghs-type-condition')
  @response(200, {
    description: 'Get top 5 observations by ghsOne.name and type-condition counts',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getTopGhsTypeConditionCounts(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // No required fields
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    // Fetch all observations with relevant relations
    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations based on the date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    // Count observations grouped by `ghsOne.name`
    const ghsCounts: GhsCounts = filteredObservations.reduce((acc: GhsCounts, observation) => {
      const ghsName = observation.ghsOne?.name ?? 'Unknown';
      acc[ghsName] = (acc[ghsName] || 0) + 1;
      return acc;
    }, {} as GhsCounts);

    // Extract top 5 GHS names
    const topGhsNames = Object.entries(ghsCounts)
      .filter(([key]) => key !== 'Unknown') // Filter out unknowns
      .sort(([, a], [, b]) => b - a) // Sort by count descending
      .slice(0, 5) // Get the top 5
      .map(([key]) => key);

    // Count type-condition combinations for top 5 GHS names
    const typeConditionCounts: Record<string, Record<string, number>> = {};

    filteredObservations.forEach((observation) => {
      const ghsName = observation.ghsOne?.name ?? '';

      // Only process if this GHS is in the top 5 list
      if (topGhsNames.includes(ghsName)) {
        try {
          const remarks = JSON.parse(observation.remarks ?? '{}');
          const conditionAct = remarks.condition_act || 'Unknown';
          const combinedKey = `${observation.type} (${conditionAct})`;

          // Filter out 'Unknown' combinations
          if (!combinedKey.includes('Unknown')) {
            if (!typeConditionCounts[ghsName]) {
              typeConditionCounts[ghsName] = {};
            }
            typeConditionCounts[ghsName][combinedKey] =
              (typeConditionCounts[ghsName][combinedKey] || 0) + 1;
          }
        } catch (error) {
          console.error('Error parsing remarks:', error);
        }
      }
    });

    // Format and return the response
    return {
      totalTopGhs: topGhsNames.length,
      topGhsNames,
      typeConditionCounts,
    };
  }

  @authenticate.skip()
  @post('/observations/top-ghs-monthly')
  @response(200, {
    description: 'Get top 5 ghsOne.name counts by month',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getTopGhsMonthly(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // `from` and `to` are optional
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    // Fetch all observations with relevant relations
    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations based on the date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    // Initialize monthly counts
    const monthlyGhsCounts: Record<string, Record<string, number>> = {};

    // Aggregate data by month and ghsOne.name
    filteredObservations.forEach((observation) => {
      const createdDate = observation.created ? new Date(observation.created) : new Date(0);
      const yearMonth = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}`;
      const ghsName = observation.ghsOne?.name || 'Unknown';

      if (!monthlyGhsCounts[yearMonth]) {
        monthlyGhsCounts[yearMonth] = {};
      }
      monthlyGhsCounts[yearMonth][ghsName] = (monthlyGhsCounts[yearMonth][ghsName] || 0) + 1;
    });

    // Extract top 5 GHS names for each month
    const topMonthlyGhs: Record<string, { name: string; count: number }[]> = {};

    Object.entries(monthlyGhsCounts).forEach(([month, ghsCount]) => {
      topMonthlyGhs[month] = Object.entries(ghsCount)
        .filter(([ghsName]) => ghsName !== 'Unknown') // Exclude 'Unknown'
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([name, count]) => ({ name, count }));
    });

    // Format and return the response
    return {
      totalMonths: Object.keys(topMonthlyGhs).length,
      topMonthlyGhs,
    };
  }

  @authenticate.skip()
  @post('/observations/category-monthly')
  @response(200, {
    description: 'Get monthly distribution of observations by category',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getCategoryMonthly(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // `from` and `to` are optional
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    // Fetch all observations with relevant relations
    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations based on the date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    // Initialize monthly category counts
    const monthlyCategoryCounts: Record<string, Record<string, number>> = {};

    // Aggregate data by month and category
    filteredObservations.forEach((observation) => {
      const createdDate = observation.created ? new Date(observation.created) : new Date(0);
      const yearMonth = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}`;
      const category = observation.category || 'Unknown';

      if (!monthlyCategoryCounts[yearMonth]) {
        monthlyCategoryCounts[yearMonth] = {};
      }
      monthlyCategoryCounts[yearMonth][category] =
        (monthlyCategoryCounts[yearMonth][category] || 0) + 1;
    });

    // Format and return the response
    return {
      totalMonths: Object.keys(monthlyCategoryCounts).length,
      monthlyCategoryCounts,
    };
  }

  @authenticate.skip()
  @post('/observations/top-reporters')
  @response(200, {
    description: 'Get top 5 reporters by number of observations reported',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getTopReporters(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [], // `from` and `to` are optional
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};

    console.log(`Received filterParams: from=${from}, to=${to}`);

    // Fetch all observations with relevant relations
    const observations = await this.observationReportRepository.find({
      include: [
        { relation: 'submitted' },
        { relation: 'actions' },
        { relation: 'workActivity' },
        { relation: 'ghsOne' },
        { relation: 'ghsTwo' },
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        { relation: 'actionOwner' },
      ],
    });

    // Filter observations based on the date range
    const filteredObservations = observations.filter((observation) => {
      const createdDate = moment(observation.created);
      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'day', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'day');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'day');
      }
      return true;
    });

    // Initialize reporter counts
    const reporterCounts: Record<string, number> = {};

    // Count number of observations per reporter
    filteredObservations.forEach((observation) => {
      const reporterName = observation.submitted?.firstName || 'Unknown';

      if (reporterName !== 'Unknown') {
        reporterCounts[reporterName] = (reporterCounts[reporterName] || 0) + 1;
      }
    });

    // Extract top 5 reporters
    const topReporters = Object.entries(reporterCounts)
      .sort(([, a], [, b]) => b - a) // Sort in descending order
      .slice(0, 5) // Take top 5
      .map(([name, count]) => ({ name, count }));

    // Format and return the response
    return {
      totalReporters: topReporters.length,
      topReporters,
    };
  }

  @patch('/observation-reports')
  @response(200, {
    description: 'ObservationReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.updateAll(observationReport, where);
  }

  @get('/observation-reports/{id}')
  @response(200, {
    description: 'ObservationReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ObservationReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.filter(ObservationReport, { exclude: 'where' }) filter?: FilterExcludingWhere<ObservationReport>
  ): Promise<ObservationReport> {
    return this.observationReportRepository.findById(id, filter);
  }

  @patch('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, { partial: true }),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @put('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.replaceById(id, observationReport);
  }

  @del('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.observationReportRepository.deleteById(id);
  }
}
