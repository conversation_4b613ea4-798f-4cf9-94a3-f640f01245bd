import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  model,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import { Action } from '../models';
import { inject } from '@loopback/core';
import { ActionRepository, AuditFindingRepository, LocationOneRepository, ReportDataRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository, AuditRepository, ObservationReportRepository, ReportIncidentRepository, PermitReportRepository, InspectionRepository, UserRepository } from '../repositories';
import { authenticate } from '@loopback/authentication';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import moment from "moment";

import { SqsService } from '../services/sqs-service.service';


@model()
export class remarksAction extends Action {
  @property({
    type: 'string',

  })
  remarks: string;
}

@authenticate('jwt')
export class ActionController {
  constructor(
    @repository(ReportIncidentRepository)
    public reportIncidentRepository: ReportIncidentRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
    @repository(AuditFindingRepository)
    public auditFindingRepository: AuditFindingRepository,
    @repository(AuditRepository)
    public auditRepository: AuditRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(ReportDataRepository)
    public reportDataRepository: ReportDataRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/actions')
  @response(200, {
    description: 'Action model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Action) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewAction',
            exclude: ['id'],
          }),
        },
      },
    })
    action: Omit<Action, 'id'>,
  ): Promise<Action> {

    return this.actionRepository.create(action);
  }

  @get('/actions/count')
  @response(200, {
    description: 'Action model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.count(where);
  }

  @get('/actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<any> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    const action = await this.actionRepository.find({
      where: {
        and: [
          { assignedToId: user?.id },
          { status: { neq: 'completed' } },
          { status: { neq: 'submitted' } }
        ]
      }
    });

    const modifiedAction = await Promise.all(action.map(async (i) => {
      let applicationDetails: any = {};

      try {
        if (!i.objectId && i.application !== 'Report') {
          console.warn(`Missing objectId for action ID: ${i.id}`);
          return { ...i, applicationDetails: null };
        }

        switch (i.application) {
          case 'Observation': {

            const obs = await this.observationReportRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
              ],
            });

            const [submittedBy, actionBy, reviewBy] = await Promise.all([
              obs?.submittedId ? this.userRepository.findById(obs.submittedId) : {},
              obs?.actionOwnerId ? this.userRepository.findById(obs.actionOwnerId) : {},
              obs?.reviewerId ? this.userRepository.findById(obs.reviewerId) : {},
            ]);

            applicationDetails = { ...obs, submittedBy, actionBy, reviewBy };
            break;
          }

          case 'INCIDENT': {

            const incident = await this.reportIncidentRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
              ],
            });

            const [submittedUser, assignedTo] = await Promise.all([
              i.submittedById ? this.userRepository.findById(i.submittedById) : {},
              i.assignedToId ? this.userRepository.findById(i.assignedToId) : {},
            ]);

            applicationDetails = { ...incident, submittedUser, assignedTo };
            break;
          }

          case 'PermitToWork': {

            const ptw = await this.permitReportRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'locationFive' },
                { relation: 'locationSix' },
              ],
            });

            const [applicant, approver, assessor, dcsoApprover] = await Promise.all([
              ptw?.applicantId ? this.userRepository.findById(ptw.applicantId) : {},
              ptw?.approverId ? this.userRepository.findById(ptw.approverId) : {},
              ptw?.assessorId ? this.userRepository.findById(ptw.assessorId) : {},
              ptw?.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
            ]);

            applicationDetails = { ...ptw, applicant, approver, assessor, dcsoApprover };
            break;
          }

          case 'Inspection': {

            const inspection = await this.inspectionRepository.findById(i.objectId, {
              include: [
                { relation: 'locationOne' },
                { relation: 'locationTwo' },
                { relation: 'locationThree' },
                { relation: 'locationFour' },
                { relation: 'checklist' },
                { relation: 'assignedTo' },
                { relation: 'actions' },
              ],
            });

            const [inspector, assignedBy] = await Promise.all([
              i.assignedToId ? this.userRepository.findById(i.assignedToId) : {},
              i.submittedById ? this.userRepository.findById(i.submittedById) : {},
            ]);

            applicationDetails = { ...inspection, inspector, assignedBy };
            break;
          }

          case 'AuditFinding': {

            const auditFinding = await this.auditFindingRepository.findById(i.objectId, {
              include: [{
                relation: "audit",
                scope: {
                  include: [
                    { relation: 'locationOne' },
                    { relation: 'locationTwo' },
                    { relation: 'locationThree' },
                    { relation: 'locationFour' },
                    { relation: 'checklist' },
                    { relation: 'assignedTo' },
                  ],
                },
              }],
            });

            applicationDetails = { ...auditFinding };
            break;
          }

          case 'Audit': {

            const audit = await this.auditRepository.findById(i.objectId);
            applicationDetails = { ...audit };
            break;
          }

          case 'Report': {

            let reports = {};
            let reportData = {};
            if (i.objectId) {
              reportData = await this.reportDataRepository.findById(i.objectId, {
                include: [
                  { relation: 'user' },
                  { relation: 'reviewer' }
                ]
              });


            }

            if (i.description) {
              reports = await this.getLocationDetails(i.description)
            }
            applicationDetails = {
              ...reports,
              ...reportData
            };
            break;

          }

          default:
            console.warn(`Unknown application type: ${i.application}`);
            applicationDetails = {};
        }
      } catch (err) {
        console.error(`Error processing action ID ${i.id}:`, err.message);
        applicationDetails = null; // fallback without throwing
      }

      return { ...i, applicationDetails };
    }));

    return modifiedAction;
  }


  @get('/actions/get/{application}')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async findAction(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
    @param.query.string('application') application?: string,
  ): Promise<any> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    const whereConditions: any[] = [
      { assignedToId: user?.id },
      { status: { neq: 'completed' } },
      { status: { neq: 'submitted' } }
    ];

    if (application) {
      whereConditions.push({ application: application });
    }

    const action = await this.actionRepository.find({
      where: {
        and: whereConditions
      }
    });
    const modifiedAction = await Promise.all(action.map(async (i) => {
      let applicationDetails: any = {};
      switch (i.application) {
        case 'Observation':
          {
            if (i.objectId) {
              const obs = await this.observationReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });

              const [submittedBy, actionBy, reviewBy] = await Promise.all([
                obs.submittedId ? this.userRepository.findById(obs.submittedId) : {},
                obs.actionOwnerId ? this.userRepository.findById(obs.actionOwnerId) : {},
                obs.reviewerId ? this.userRepository.findById(obs.reviewerId) : {},
              ]);
              applicationDetails = {
                ...obs,
                submittedBy,
                actionBy,
                reviewBy,
              };
            }
          }
          break;

        case 'INCIDENT':
          {
            if (i.objectId) {
              const incident = await this.reportIncidentRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },
                ],
              });

              const [submittedUser, assignedTo] = await Promise.all([
                i.submittedById ? this.userRepository.findById(i.submittedById) : {},
                i.assignedToId ? this.userRepository.findById(i.assignedToId) : {},

              ]);
              applicationDetails = {
                ...incident,
                submittedUser,
                assignedTo
              };
            }
          }

          break;

        case 'PermitToWork':
          {
            if (i.objectId) {
              const ptw = await this.permitReportRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'locationFive' },
                  { relation: 'locationSix' },

                ],
              });

              const [applicant, approver, assessor, dcsoApprover] = await Promise.all([
                ptw.applicantId ? this.userRepository.findById(ptw.applicantId) : {},
                ptw.approverId ? this.userRepository.findById(ptw.approverId) : {},
                ptw.assessorId ? this.userRepository.findById(ptw.assessorId) : {},
                ptw.dcsoApproverId ? this.userRepository.findById(ptw.dcsoApproverId) : {},
              ]);
              applicationDetails = {
                ...ptw,
                applicant,
                approver,
                assessor,
                dcsoApprover
              };
            }
          }
          break;

        case 'Inspection':
          {
            if (i.objectId) {
              const inspection = await this.inspectionRepository.findById(i.objectId, {
                include: [
                  { relation: 'locationOne' },
                  { relation: 'locationTwo' },
                  { relation: 'locationThree' },
                  { relation: 'locationFour' },
                  { relation: 'checklist' },
                  { relation: 'assignedTo' },
                  { relation: 'actions' }
                ],
              });

              const [inspector, assignedBy] = await Promise.all([
                i.assignedToId ? this.userRepository.findById(i.assignedToId) : {},
                i.submittedById ? this.userRepository.findById(i.submittedById) : {},

              ]);
              applicationDetails = {
                ...inspection,
                inspector,
                assignedBy,

              };
            }
          }
          break;

        case 'AuditFinding':
          {
            if (i.objectId) {
              const auditFinding = await this.auditFindingRepository.findById(i.objectId, {
                include: [{
                  relation: "audit",
                  scope: {
                    include: [
                      { relation: 'locationOne' },
                      { relation: 'locationTwo' },
                      { relation: 'locationThree' },
                      { relation: 'locationFour' },
                      { relation: 'checklist' },
                      { relation: 'assignedTo' }]
                  }
                }]
              });

              applicationDetails = {
                ...auditFinding,
              };
            }
          }
          break;

        case 'Audit':
          {
            if (i.objectId) {
              const audit = await this.auditRepository.findById(i.objectId);

              applicationDetails = {
                ...audit,
              };
            }
          }
          break;

        case 'Report':
          {
            let reports = {};
            let reportData = {};
            if (i.description) {
              reports = await this.getLocationDetails(i.description);
            }
            if (i.objectId) {
              reportData = await this.reportDataRepository.findById(i.objectId, {
                include: [
                  { relation: 'user' },
                  { relation: 'reviewer' }
                ]
              });
            }

            applicationDetails = {
              ...reports,
              ...reportData
            };
          }
          break;
      }
      return { ...i, applicationDetails: applicationDetails };
    }));

    return modifiedAction;
  }

  async getLocationDetails(
    locationFourId: string,
  ): Promise<any> {
    let locationFour, locationThree, locationTwo, locationOne;

    try {
      locationFour = await this.locationFourRepository.findById(locationFourId);
    } catch (error) {
      locationFour = { id: '', name: '', locationThreeId: '' };
    }

    try {
      locationThree = await this.locationThreeRepository.findById(locationFour.locationThreeId);
    } catch (error) {
      locationThree = { id: '', name: '', locationTwoId: '' };
    }

    try {
      locationTwo = await this.locationTwoRepository.findById(locationThree.locationTwoId);
    } catch (error) {
      locationTwo = { id: '', name: '', locationOneId: '' };
    }

    try {
      locationOne = await this.locationOneRepository.findById(locationTwo.locationOneId);
    } catch (error) {
      locationOne = { id: '', name: '' };
    }

    return {
      locationOne: {
        id: locationOne.id,
        name: locationOne.name,
      },
      locationTwo: {
        id: locationTwo.id,
        name: locationTwo.name,
        locationOneId: locationTwo.locationOneId,
      },
      locationThree: {
        id: locationThree.id,
        name: locationThree.name,
        locationTwoId: locationThree.locationTwoId,
      },
      locationFour: {
        id: locationFour.id,
        name: locationFour.name,
        locationThreeId: locationFour.locationThreeId,
      },
    };
  }

  @get('/my-actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, { includeRelations: true }),
        },
      },
    },
  })
  async findMy(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    return this.actionRepository.find({ where: { submittedById: user?.id } });
  }

  @patch('/actions')
  @response(200, {
    description: 'Action PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.updateAll(action, where);
  }

  @get('/actions/{id}')
  @response(200, {
    description: 'Action model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Action, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Action, { exclude: 'where' }) filter?: FilterExcludingWhere<Action>
  ): Promise<Action> {
    return this.actionRepository.findById(id, filter);
  }

  @patch('/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(remarksAction, { partial: true }),
        },
      },
    })
    action: remarksAction,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    action.status = 'submitted'
    if (action.actionType === 'reviewer') {
      delete action.actionType
      await this.observationReportRepository.updateById(action.objectId, { remarks: action?.remarks || '', status: 'At Risk - Actions to be Verified', evidences: action.uploads, actionTaken: action.actionTaken, reviewerId: action.assignedToId });


      const actionItem = {
        application: "Observation",
        actionType: "reviewer",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }


      if (action.assignedToId && action.objectId) {

        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const reviewerDetails = await this.userRepository.findById(action.assignedToId)

        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const mailSubject = `${observationDetails.maskId} - Review Action: ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <h1>Observation Report - </h1>
           
            <p><strong>Name of Assignee: </strong> ${user?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
           
            <p><strong>Rectification action taken by Assignee: </strong> ${action.actionTaken}</p>
            <p>Please login to the EHS Digital Application or AcuiZen Mobile Application to take action </p>
        </body>
        </html>`;


        if (reviewerDetails) { this.sqsService.sendMessage(reviewerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      actionItem.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'approve') {

      if (action.objectId) {
        await this.observationReportRepository.updateById(action.objectId, { remarks: action?.remarks || '', status: 'At Risk - Closed', actionTaken: action.actionTaken });
        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })

        if (observationDetails.submittedId && observationDetails.actionOwnerId && observationDetails.reviewerId) {


          const locationOneName = (observationDetails as any).locationOne?.name;
          const locationTwoName = (observationDetails as any).locationTwo?.name;
          const locationThreeName = (observationDetails as any).locationThree?.name;
          const locationFourName = (observationDetails as any).locationFour?.name;
          const locationFiveName = (observationDetails as any).locationFive?.name;
          const locationSixName = (observationDetails as any).locationSix?.name;

          const actionOwnerUser = await this.userRepository.findById(observationDetails.actionOwnerId);
          const reviewerUser = await this.userRepository.findById(observationDetails.reviewerId);

          const mailSubject = `${observationDetails.maskId} - Action Approved: ${locationFiveName}, ${locationSixName}`;
          const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <h1>Observation Report - ${observationDetails.maskId}</h1>
           
            <p><strong>Name of Reviewer: </strong> ${reviewerUser?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
           
            <p><strong>Action taken by Assignee: </strong> ${action.actionTaken}</p>
            <p>Please login to the EHS Digital Application or AcuiZen Mobile Application to take action </p>
        </body>
        </html>`;

          if (actionOwnerUser) { this.sqsService.sendMessage(actionOwnerUser, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }


        }

      }

    }

    if (action.actionType === 'reject') {
      await this.observationReportRepository.updateById(action.objectId, { remarks: action?.remarks || '', status: 'At Risk - Actions Re-Assigned', actionOwnerId: action.assignedToId });
      const actionItem = {
        application: "Observation",
        actionType: "action_owner",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        dueDate: action.dueDate,
        status: "returned",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        uploads: action.uploads,
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }
      if (action.assignedToId && action.objectId) {

        const observationDetails = await this.observationReportRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })
        const locationOneName = (observationDetails as any).locationOne?.name;
        const locationTwoName = (observationDetails as any).locationTwo?.name;
        const locationThreeName = (observationDetails as any).locationThree?.name;
        const locationFourName = (observationDetails as any).locationFour?.name;
        const locationFiveName = (observationDetails as any).locationFive?.name;
        const locationSixName = (observationDetails as any).locationSix?.name;

        const actionOwnerDetails = await this.userRepository.findById(action.assignedToId)


        const mailSubject = `${observationDetails.maskId} Action Returned. Resubmit Action: ${locationFiveName}, ${locationSixName}`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Observation Report Email</title>
        </head>
        <body>
            <h1>Observation Report - ${observationDetails.maskId}</h1>
           
            <p><strong>Name of Reviewer: </strong> ${user?.firstName}</p>
            <p><strong>Location: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName} &gt; ${locationFiveName} &gt; ${locationSixName}</p>
            <p><strong>Description: </strong> ${observationDetails.description}</p>
            <p><strong>Comments from the reviewer: </strong> ${action.comments}</p>
            <p><strong>Assignee's Action due date and time:</strong> ${action.dueDate}</p>
            <p>Please login to the EHS Digital Application or AcuiZen Mobile Application to take action </p>
        </body>
        </html>`;
        if (actionOwnerDetails) { this.sqsService.sendMessage(actionOwnerDetails, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
      actionItem.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    action.createdDate = moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ');
    await this.actionRepository.updateById(id, action);
  }



  @patch('/actions-read/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const actionData = await this.actionRepository.findById(id)

    if (actionData.assignedToId === user?.id)
      action.status = 'read'

    await this.actionRepository.updateById(id, action);
  }

  @put('/actions/{id}')
  @response(204, {
    description: 'Action PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() action: Action,
  ): Promise<void> {
    await this.actionRepository.replaceById(id, action);
  }

  @del('/actions/{id}')
  @response(204, {
    description: 'Action DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.actionRepository.deleteById(id);
  }
}
